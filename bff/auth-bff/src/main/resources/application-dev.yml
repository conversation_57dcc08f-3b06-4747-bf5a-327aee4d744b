spring:
  main:
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude:
    - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
    - com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration
    - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  data:
    redis:
      host: localhost
      port: 6389
      database: 0
      password: null
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  redis:
    redisson:
      config: "singleServerConfig:\n  address: \"redis://localhost:6389\"\n  database:\
        \ 0\n  connectionPoolSize: 64\n  connectionMinimumIdleSize: 10\n  timeout:\
        \ 10000\n  retryAttempts: 3\n  retryInterval: 1500\n"
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: dev
        enabled: true
        ip: 127.0.0.1
        instance-id: ${spring.application.name}:${server.port}
        group: dev
        service: ${spring.application.name}
        username: nacos
        password: Veritrans9900
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yml
        namespace: dev
        enabled: true
        username: nacos
        password: Veritrans9900
        shared-configs:
        - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        group: dev
logging:
  level:
    com.kxpms: INFO
    org.springframework: WARN
    cn.dev33.satoken: DEBUG
    com.kxpms.bff.common.security: DEBUG

# 开发环境禁用权限检查
permission:
  check:
    enabled: false
