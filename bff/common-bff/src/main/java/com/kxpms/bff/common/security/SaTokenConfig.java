package com.kxpms.bff.common.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * BFF层 Sa-Token 配置类
 * 专门用于BFF层的API访问控制和Token认证
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    @Value("${spring.profiles.active:prod}")
    private String activeProfile;

    @Value("${permission.check.enabled:true}")
    private boolean permissionCheckEnabled;

    // Sa-Token过滤器已完全禁用，使用拦截器方式

    /**
     * 注册Sa-Token拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("BFF Sa-Token拦截器配置: 当前环境: {}, 权限检查启用: {}", activeProfile, permissionCheckEnabled);

        // 如果权限检查被禁用，完全不注册Sa-Token拦截器
        if (!permissionCheckEnabled) {
            log.info("BFF Sa-Token拦截器: 权限检查已禁用，不注册Sa-Token拦截器");
            return;
        }

        // 如果是开发环境，注册拦截器但跳过验证码等公开接口
        if ("dev".equals(activeProfile)) {
            log.info("BFF Sa-Token拦截器: 开发环境，注册拦截器但跳过公开接口验证");
        }

        // 注册Sa-Token的路由拦截器，使用自定义认证逻辑
        registry.addInterceptor(new SaInterceptor(handler -> {
            String currentPath = SaHolder.getRequest().getRequestPath();
            log.info("BFF Sa-Token拦截器认证: 当前环境: {}, 请求路径: {}", activeProfile, currentPath);

            // 登录校验 -- 拦截所有路由，并排除登录等接口
            SaRouter.match("/**")
                .notMatch(
                    // 原有认证相关接口
                    "/auth/login",
                    "/auth/register",
                    "/auth/captcha",
                    "/auth/captcha/**",
                    "/auth/token/**",
                    "/system/auth/login",
                    "/system/auth/register",
                    // 标准化BFF认证接口
                    "/bff/auth/v3/login",
                    "/bff/auth/v3/register",
                    "/bff/auth/v3/captcha",
                    "/bff/auth/v3/captcha/**",
                    "/bff/auth/v3/logout",
                    "/bff/auth/v3/matchTenant",
                    "/bff/auth/v3/validateTenantApp",
                    "/bff/auth/v3/getTenantAppConfig",
                    "/bff/auth/v3/findTenantId",
                    // wxapp-bff 公开接口
                    "/bff/wxapp/v3/user-services/login",
                    "/bff/wxapp/v3/user-services/verification-code",
                    // Swagger UI 相关
                    "/swagger-ui/**",
                    "/swagger-ui.html",
                    "/swagger-resources/**",
                    "/webjars/**",
                    "/v3/api-docs/**",
                    // 其他静态资源
                    "/favicon.ico",
                    "/doc.html",
                    "/actuator/**",
                    "/inner/**",
                    // 临时测试接口
                    "/test/public"
                )
                .check(r -> {
                    String requestPath = SaHolder.getRequest().getRequestPath();
                    log.info("BFF Sa-Token拦截器: 需要认证的请求路径: {}", requestPath);

                    // 开发环境下，对验证码等公开接口跳过认证
                    if ("dev".equals(activeProfile)) {
                        if (requestPath.startsWith("/bff/auth/v3/captcha") ||
                            requestPath.equals("/bff/auth/v3/captcha") ||
                            requestPath.startsWith("/auth/captcha")) {
                            log.info("BFF Sa-Token拦截器: 开发环境，跳过验证码接口认证: {}", requestPath);
                            return;
                        }
                    }

                    // 对于web-bff的所有API，完全跳过登录和权限检查
                    if (requestPath.startsWith("/bff/web/")) {
                        log.info("BFF Sa-Token拦截器: web-bff路径，跳过所有验证: {}", requestPath);
                        // 模拟登录状态，使用固定的用户ID
                        StpUtil.login("web-bff-admin");
                        return;
                    }

                    // 处理Bearer token（auth-bff等其他服务需要）
                    String authHeader = SaHolder.getRequest().getHeader("Authorization");
                    if (authHeader != null && authHeader.startsWith("Bearer ")) {
                        String token = authHeader.substring(7);
                        try {
                            // 对于auth-bff，正常验证
                            if (StpUtil.getLoginIdByToken(token) != null) {
                                StpUtil.setTokenValue(token);
                                log.info("BFF Sa-Token拦截器: Bearer token验证成功, 路径: {}", requestPath);
                                return;
                            }
                        } catch (Exception e) {
                            log.warn("BFF Sa-Token拦截器: Bearer token验证失败, 路径: {}, 错误: {}", requestPath, e.getMessage());
                        }
                    }

                    // 处理satoken头（兼容旧版本）
                    String saTokenHeader = SaHolder.getRequest().getHeader("satoken");
                    if (saTokenHeader != null && !saTokenHeader.isEmpty()) {
                        try {
                            if (StpUtil.getLoginIdByToken(saTokenHeader) != null) {
                                StpUtil.setTokenValue(saTokenHeader);
                                log.info("BFF Sa-Token拦截器: satoken头验证成功, 路径: {}", requestPath);
                                return;
                            }
                        } catch (Exception e) {
                            log.warn("BFF Sa-Token拦截器: satoken头验证失败, 路径: {}, 错误: {}", requestPath, e.getMessage());
                        }
                    }

                    // 检查登录状态
                    log.warn("BFF Sa-Token拦截器: 未找到有效token，执行登录检查, 路径: {}", requestPath);
                    StpUtil.checkLogin();
                });
        }))
            .addPathPatterns("/**");
        log.info("BFF Sa-Token拦截器: 已注册，环境: {}", activeProfile);
    }
}
