package com.kxpms.bff.web;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kxpms.bff.web.monitor.ApiMonitoringService;

import lombok.extern.slf4j.Slf4j;

/**
 * API连通性测试
 * 
 * 测试Web-BFF各个接口的可达性和基本功能
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class ApiConnectivityTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ApiMonitoringService apiMonitoringService;

    @Autowired
    private ObjectMapper objectMapper;

    private String getBaseUrl() {
        return "http://localhost:" + port;
    }

    /**
     * 测试监控接口
     */
    @Test
    public void testMonitoringApis() {
        log.info("开始测试监控相关接口");

        // 测试监控概览
        testApiEndpoint("GET", "/bff/web/v3/monitor/overview", null, "监控概览");

        // 测试API统计
        testApiEndpoint("GET", "/bff/web/v3/monitor/statistics", null, "API统计");

        // 测试健康状态
        testApiEndpoint("GET", "/bff/web/v3/monitor/health", null, "健康状态");

        // 测试仪表板
        testApiEndpoint("GET", "/bff/web/v3/monitor/dashboard", null, "监控仪表板");

        log.info("监控接口测试完成");
    }

    /**
     * 测试认证接口
     */
    @Test
    public void testAuthApis() {
        log.info("开始测试认证相关接口");

        // 测试登录接口
        Map<String, Object> loginParams = new HashMap<>();
        loginParams.put("username", "test");
        loginParams.put("password", "test123");
        loginParams.put("tenantId", "1");
        
        testApiEndpoint("POST", "/bff/web/v3/auth/login", loginParams, "用户登录");

        // 测试注册接口
        Map<String, Object> registerParams = new HashMap<>();
        registerParams.put("username", "testuser");
        registerParams.put("password", "test123");
        registerParams.put("email", "<EMAIL>");
        registerParams.put("phone", "13800138000");
        registerParams.put("companyName", "测试公司");
        
        testApiEndpoint("POST", "/bff/web/v3/auth/register", registerParams, "用户注册");

        // 测试获取用户信息（无需token的测试）
        testApiEndpoint("GET", "/bff/web/v3/auth/getInfo", null, "获取用户信息");

        // 测试获取路由信息
        testApiEndpoint("GET", "/bff/web/v3/auth/getRouters", null, "获取路由信息");

        log.info("认证接口测试完成");
    }

    /**
     * 测试诊断接口
     */
    @Test
    public void testDiagnosticApis() {
        log.info("开始测试诊断相关接口");

        // 测试诊断报告
        testApiEndpoint("GET", "/bff/web/v3/diagnostic/report", null, "诊断报告");

        // 测试健康检查
        testApiEndpoint("POST", "/bff/web/v3/diagnostic/health-check", null, "实时健康检查");

        // 测试问题API列表
        testApiEndpoint("GET", "/bff/web/v3/diagnostic/problem-apis", null, "问题API列表");

        // 测试API测试功能
        Map<String, Object> testParams = new HashMap<>();
        testParams.put("apiPath", "/bff/web/v3/monitor/overview");
        testParams.put("method", "GET");
        
        testApiEndpoint("POST", "/bff/web/v3/diagnostic/test-api", testParams, "API测试");

        log.info("诊断接口测试完成");
    }

    /**
     * 测试公共接口
     */
    @Test
    public void testCommonApis() {
        log.info("开始测试公共接口");

        // 测试部门树
        testApiEndpoint("GET", "/bff/web/v3/common/departments/tree", null, "部门树");

        log.info("公共接口测试完成");
    }

    /**
     * 测试业务接口样例
     */
    @Test
    public void testBusinessApis() {
        log.info("开始测试业务接口样例");

        // 测试BLL测试接口
        testApiEndpoint("GET", "/bff/web/v3/test/bll/status", null, "BLL服务状态");
        testApiEndpoint("GET", "/bff/web/v3/test/bll/statistics", null, "BLL服务统计");

        log.info("业务接口测试完成");
    }

    /**
     * 执行完整的API连通性测试
     */
    @Test
    public void testFullApiConnectivity() {
        log.info("开始执行完整的API连通性测试");

        long startTime = System.currentTimeMillis();

        try {
            // 等待应用完全启动
            Thread.sleep(2000);

            // 执行各类接口测试
            testMonitoringApis();
            testAuthApis();
            testDiagnosticApis();
            testCommonApis();
            testBusinessApis();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("完整API连通性测试完成，耗时: {}ms", duration);

            // 输出监控统计
            Map<String, Object> statistics = apiMonitoringService.getApiStatistics();
            log.info("测试期间API调用统计: {}", statistics);

        } catch (Exception e) {
            log.error("API连通性测试失败", e);
            throw new RuntimeException("API连通性测试失败", e);
        }
    }

    /**
     * 测试单个API端点
     */
    private void testApiEndpoint(String method, String path, Object requestBody, String description) {
        try {
            String url = getBaseUrl() + path;
            long startTime = System.currentTimeMillis();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            ResponseEntity<String> response;

            if ("GET".equalsIgnoreCase(method)) {
                HttpEntity<Void> entity = new HttpEntity<>(headers);
                response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            } else if ("POST".equalsIgnoreCase(method)) {
                HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
                response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            } else {
                log.warn("不支持的HTTP方法: {}", method);
                return;
            }

            long responseTime = System.currentTimeMillis() - startTime;
            HttpStatusCode statusCode = response.getStatusCode();

            // 记录测试结果
            boolean success = statusCode.is2xxSuccessful();
            String resultStatus = success ? "成功" : "失败";
            
            log.info("API测试 [{}] {} {} - 状态: {} ({}), 响应时间: {}ms", 
                description, method, path, resultStatus, statusCode.value(), responseTime);

            if (!success) {
                log.warn("API响应内容: {}", response.getBody());
            }

            // 记录到监控系统
            apiMonitoringService.recordApiCall(path, method, responseTime, success, 
                success ? null : String.valueOf(statusCode.value()));

        } catch (Exception e) {
            log.error("API测试失败 [{}] {} {}: {}", description, method, path, e.getMessage());
            
            // 记录失败到监控系统
            apiMonitoringService.recordApiCall(path, method, 5000L, false, "TEST_ERROR");
        }
    }

    /**
     * 性能压力测试
     */
    @Test
    public void testApiPerformance() {
        log.info("开始API性能测试");

        String testPath = "/bff/web/v3/monitor/overview";
        int testCount = 50;
        long totalTime = 0;
        int successCount = 0;

        for (int i = 0; i < testCount; i++) {
            try {
                long startTime = System.currentTimeMillis();
                
                ResponseEntity<String> response = restTemplate.getForEntity(
                    getBaseUrl() + testPath, String.class);
                
                long responseTime = System.currentTimeMillis() - startTime;
                totalTime += responseTime;

                if (response.getStatusCode().is2xxSuccessful()) {
                    successCount++;
                }

                // 短暂间隔
                Thread.sleep(10);

            } catch (Exception e) {
                log.warn("性能测试第{}次请求失败: {}", i + 1, e.getMessage());
            }
        }

        double avgResponseTime = (double) totalTime / testCount;
        double successRate = (double) successCount / testCount * 100;

        log.info("性能测试结果 - 总请求: {}, 成功: {}, 成功率: {:.2f}%, 平均响应时间: {:.2f}ms",
            testCount, successCount, successRate, avgResponseTime);

        // 性能评估
        if (avgResponseTime < 100) {
            log.info("性能评级: 优秀 (平均响应时间 < 100ms)");
        } else if (avgResponseTime < 500) {
            log.info("性能评级: 良好 (平均响应时间 < 500ms)");
        } else if (avgResponseTime < 1000) {
            log.info("性能评级: 一般 (平均响应时间 < 1000ms)");
        } else {
            log.warn("性能评级: 需要优化 (平均响应时间 >= 1000ms)");
        }
    }
}
