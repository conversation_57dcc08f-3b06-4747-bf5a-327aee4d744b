package com.kxpms.bff.web.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kxpms.bff.web.monitor.ApiMonitoringService;
import com.kxpms.bff.web.monitor.ApiMonitoringService.AlertRecord;
import com.kxpms.bff.web.service.ApiHealthCheckService.HealthCheckResult;

import lombok.extern.slf4j.Slf4j;

/**
 * API监控报告服务
 * 
 * 生成各种API监控报告
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiReportService {

    @Autowired
    private ApiMonitoringService apiMonitoringService;
    
    @Autowired
    private ApiHealthCheckService apiHealthCheckService;

    /**
     * 生成API状况总结报告
     */
    public Map<String, Object> generateApiStatusReport() {
        log.info("生成API状况总结报告");
        
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 获取基础统计数据
            Map<String, Object> apiStats = apiMonitoringService.getApiStatistics();
            Map<String, HealthCheckResult> healthResults = apiHealthCheckService.getCachedHealthResults();
            List<AlertRecord> recentAlerts = apiMonitoringService.getAlertHistory(20);
            
            // 报告基本信息
            report.put("reportTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            report.put("reportType", "API状况总结报告");
            
            // 总体概况
            Map<String, Object> overview = generateOverview(apiStats, healthResults);
            report.put("overview", overview);
            
            // 问题分析
            Map<String, Object> problemAnalysis = analyzeProblemApis(apiStats);
            report.put("problemAnalysis", problemAnalysis);
            
            // 性能分析
            Map<String, Object> performanceAnalysis = analyzePerformance(apiStats);
            report.put("performanceAnalysis", performanceAnalysis);
            
            // 健康状态分析
            Map<String, Object> healthAnalysis = analyzeHealthStatus(healthResults);
            report.put("healthAnalysis", healthAnalysis);
            
            // 告警分析
            Map<String, Object> alertAnalysis = analyzeAlerts(recentAlerts);
            report.put("alertAnalysis", alertAnalysis);
            
            // 改进建议
            List<String> recommendations = generateRecommendations(apiStats, healthResults, recentAlerts);
            report.put("recommendations", recommendations);
            
            // 评分
            int overallScore = calculateOverallScore(apiStats, healthResults, recentAlerts);
            report.put("overallScore", overallScore);
            report.put("scoreLevel", getScoreLevel(overallScore));
            
            log.info("API状况总结报告生成完成，总体评分: {}", overallScore);
            
        } catch (Exception e) {
            log.error("生成API状况报告失败", e);
            report.put("error", "报告生成失败: " + e.getMessage());
        }
        
        return report;
    }

    /**
     * 生成API性能报告
     */
    public Map<String, Object> generatePerformanceReport() {
        log.info("生成API性能报告");
        
        Map<String, Object> report = new HashMap<>();
        
        try {
            Map<String, Object> apiStats = apiMonitoringService.getApiStatistics();
            
            report.put("reportTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            report.put("reportType", "API性能分析报告");
            
            // 性能统计
            report.put("performanceStats", analyzePerformance(apiStats));
            
            // 慢接口分析
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> apiDetails = (List<Map<String, Object>>) apiStats.get("apiDetails");
            List<Map<String, Object>> slowApis = apiDetails.stream()
                .filter(api -> (Long) api.get("avgResponseTime") > 3000)
                .sorted((a, b) -> Long.compare((Long) b.get("avgResponseTime"), (Long) a.get("avgResponseTime")))
                .limit(10)
                .collect(Collectors.toList());
            report.put("slowApis", slowApis);
            
            // 高频接口分析
            List<Map<String, Object>> highFrequencyApis = apiDetails.stream()
                .sorted((a, b) -> Long.compare((Long) b.get("totalCalls"), (Long) a.get("totalCalls")))
                .limit(10)
                .collect(Collectors.toList());
            report.put("highFrequencyApis", highFrequencyApis);
            
            // 性能建议
            List<String> performanceTips = generatePerformanceTips(apiDetails);
            report.put("performanceTips", performanceTips);
            
        } catch (Exception e) {
            log.error("生成API性能报告失败", e);
            report.put("error", "性能报告生成失败: " + e.getMessage());
        }
        
        return report;
    }

    /**
     * 生成问题API报告
     */
    public Map<String, Object> generateProblemApiReport() {
        log.info("生成问题API报告");
        
        Map<String, Object> report = new HashMap<>();
        
        try {
            Map<String, Object> apiStats = apiMonitoringService.getApiStatistics();
            
            report.put("reportTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            report.put("reportType", "问题API分析报告");
            
            // 问题API分析
            Map<String, Object> problemAnalysis = analyzeProblemApis(apiStats);
            report.put("problemAnalysis", problemAnalysis);
            
            // 错误分类统计
            Map<String, Object> errorClassification = classifyErrors(apiStats);
            report.put("errorClassification", errorClassification);
            
            // 解决方案建议
            List<String> solutions = generateSolutions(problemAnalysis);
            report.put("solutions", solutions);
            
        } catch (Exception e) {
            log.error("生成问题API报告失败", e);
            report.put("error", "问题API报告生成失败: " + e.getMessage());
        }
        
        return report;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成总体概况
     */
    private Map<String, Object> generateOverview(Map<String, Object> apiStats, 
            Map<String, HealthCheckResult> healthResults) {
        Map<String, Object> overview = new HashMap<>();
        
        // API统计概况
        overview.put("totalApis", apiStats.get("totalApis"));
        overview.put("totalCalls", apiStats.get("totalCalls"));
        overview.put("totalErrors", apiStats.get("totalErrors"));
        overview.put("overallErrorRate", apiStats.get("overallErrorRate"));
        
        // 服务健康概况
        long totalServices = healthResults.size();
        long healthyServices = healthResults.values().stream()
            .mapToLong(result -> result.isHealthy() ? 1 : 0)
            .sum();
        
        overview.put("totalServices", totalServices);
        overview.put("healthyServices", healthyServices);
        overview.put("serviceHealthRate", totalServices > 0 ? (double) healthyServices / totalServices : 0.0);
        
        // 整体状态评估
        double errorRate = (Double) apiStats.get("overallErrorRate");
        double serviceHealthRate = (double) healthyServices / Math.max(totalServices, 1);
        
        String overallStatus;
        if (errorRate < 0.01 && serviceHealthRate > 0.95) {
            overallStatus = "优秀";
        } else if (errorRate < 0.03 && serviceHealthRate > 0.8) {
            overallStatus = "良好";
        } else if (errorRate < 0.05 && serviceHealthRate > 0.6) {
            overallStatus = "一般";
        } else {
            overallStatus = "需要关注";
        }
        
        overview.put("overallStatus", overallStatus);
        
        return overview;
    }

    /**
     * 分析问题API
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> analyzeProblemApis(Map<String, Object> apiStats) {
        Map<String, Object> analysis = new HashMap<>();
        
        List<Map<String, Object>> apiDetails = (List<Map<String, Object>>) apiStats.get("apiDetails");
        
        // 高错误率API
        List<Map<String, Object>> highErrorApis = apiDetails.stream()
            .filter(api -> (Double) api.get("errorRate") > 0.05)
            .sorted((a, b) -> Double.compare((Double) b.get("errorRate"), (Double) a.get("errorRate")))
            .limit(10)
            .collect(Collectors.toList());
        
        // 慢响应API
        List<Map<String, Object>> slowApis = apiDetails.stream()
            .filter(api -> (Long) api.get("avgResponseTime") > 5000)
            .sorted((a, b) -> Long.compare((Long) b.get("avgResponseTime"), (Long) a.get("avgResponseTime")))
            .limit(10)
            .collect(Collectors.toList());
        
        analysis.put("highErrorApis", highErrorApis);
        analysis.put("slowApis", slowApis);
        analysis.put("totalProblemApis", highErrorApis.size() + slowApis.size());
        
        return analysis;
    }

    /**
     * 分析性能
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> analyzePerformance(Map<String, Object> apiStats) {
        Map<String, Object> analysis = new HashMap<>();
        
        List<Map<String, Object>> apiDetails = (List<Map<String, Object>>) apiStats.get("apiDetails");
        
        if (!apiDetails.isEmpty()) {
            // 计算平均响应时间
            double avgResponseTime = apiDetails.stream()
                .mapToLong(api -> (Long) api.get("avgResponseTime"))
                .average()
                .orElse(0.0);
            
            // 计算最大响应时间
            long maxResponseTime = apiDetails.stream()
                .mapToLong(api -> (Long) api.get("maxResponseTime"))
                .max()
                .orElse(0L);
            
            // 计算最小响应时间
            long minResponseTime = apiDetails.stream()
                .mapToLong(api -> (Long) api.get("minResponseTime"))
                .min()
                .orElse(0L);
            
            analysis.put("avgResponseTime", avgResponseTime);
            analysis.put("maxResponseTime", maxResponseTime);
            analysis.put("minResponseTime", minResponseTime);
            
            // 性能分级
            String performanceGrade;
            if (avgResponseTime < 500) {
                performanceGrade = "优秀";
            } else if (avgResponseTime < 1000) {
                performanceGrade = "良好";
            } else if (avgResponseTime < 3000) {
                performanceGrade = "一般";
            } else {
                performanceGrade = "需要优化";
            }
            
            analysis.put("performanceGrade", performanceGrade);
        }
        
        return analysis;
    }

    /**
     * 分析健康状态
     */
    private Map<String, Object> analyzeHealthStatus(Map<String, HealthCheckResult> healthResults) {
        Map<String, Object> analysis = new HashMap<>();
        
        List<HealthCheckResult> unhealthyServices = healthResults.values().stream()
            .filter(result -> !result.isHealthy())
            .collect(Collectors.toList());
        
        analysis.put("unhealthyServices", unhealthyServices);
        analysis.put("unhealthyCount", unhealthyServices.size());
        
        return analysis;
    }

    /**
     * 分析告警
     */
    private Map<String, Object> analyzeAlerts(List<AlertRecord> alerts) {
        Map<String, Object> analysis = new HashMap<>();
        
        analysis.put("totalAlerts", alerts.size());
        
        // 按类型分组
        Map<String, Long> alertsByType = alerts.stream()
            .collect(Collectors.groupingBy(AlertRecord::getAlertType, Collectors.counting()));
        
        analysis.put("alertsByType", alertsByType);
        
        return analysis;
    }

    /**
     * 生成改进建议
     */
    private List<String> generateRecommendations(Map<String, Object> apiStats, 
            Map<String, HealthCheckResult> healthResults, List<AlertRecord> alerts) {
        List<String> recommendations = new ArrayList<>();
        
        double errorRate = (Double) apiStats.get("overallErrorRate");
        if (errorRate > 0.05) {
            recommendations.add("整体错误率偏高，建议检查业务逻辑和异常处理机制");
        }
        
        long unhealthyServices = healthResults.values().stream()
            .mapToLong(result -> result.isHealthy() ? 0 : 1)
            .sum();
        if (unhealthyServices > 0) {
            recommendations.add(String.format("有 %d 个服务不健康，建议检查服务状态和依赖", unhealthyServices));
        }
        
        if (alerts.size() > 10) {
            recommendations.add("告警数量较多，建议优化监控阈值或修复相关问题");
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("系统运行状态良好，继续保持当前水平");
        }
        
        return recommendations;
    }

    /**
     * 计算总体评分
     */
    private int calculateOverallScore(Map<String, Object> apiStats, 
            Map<String, HealthCheckResult> healthResults, List<AlertRecord> alerts) {
        int score = 100;
        
        // 基于错误率扣分
        double errorRate = (Double) apiStats.get("overallErrorRate");
        score -= (int) (errorRate * 1000); // 每1%错误率扣10分
        
        // 基于不健康服务扣分
        long unhealthyServices = healthResults.values().stream()
            .mapToLong(result -> result.isHealthy() ? 0 : 1)
            .sum();
        score -= (int) (unhealthyServices * 15); // 每个不健康服务扣15分
        
        // 基于告警数量扣分
        if (alerts.size() > 20) {
            score -= 20;
        } else if (alerts.size() > 10) {
            score -= 10;
        }
        
        return Math.max(0, Math.min(100, score));
    }

    /**
     * 获取评分等级
     */
    private String getScoreLevel(int score) {
        if (score >= 90) {
            return "优秀";
        } else if (score >= 80) {
            return "良好";
        } else if (score >= 70) {
            return "一般";
        } else if (score >= 60) {
            return "及格";
        } else {
            return "需要改进";
        }
    }

    /**
     * 生成性能优化建议
     */
    private List<String> generatePerformanceTips(List<Map<String, Object>> apiDetails) {
        List<String> tips = new ArrayList<>();
        
        // 分析慢接口
        long slowApiCount = apiDetails.stream()
            .mapToLong(api -> (Long) api.get("avgResponseTime") > 3000 ? 1 : 0)
            .sum();
        
        if (slowApiCount > 0) {
            tips.add("发现 " + slowApiCount + " 个慢接口，建议优化数据库查询或增加缓存");
        }
        
        // 分析高频接口
        List<Map<String, Object>> highFrequencyApis = apiDetails.stream()
            .filter(api -> (Long) api.get("totalCalls") > 1000)
            .collect(Collectors.toList());
        
        if (!highFrequencyApis.isEmpty()) {
            tips.add("高频接口建议增加缓存策略，减少数据库压力");
        }
        
        if (tips.isEmpty()) {
            tips.add("API性能表现良好，继续保持");
        }
        
        return tips;
    }

    /**
     * 错误分类
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> classifyErrors(Map<String, Object> apiStats) {
        Map<String, Object> classification = new HashMap<>();
        
        List<Map<String, Object>> apiDetails = (List<Map<String, Object>>) apiStats.get("apiDetails");
        
        // 按错误率分类
        long highErrorApis = apiDetails.stream()
            .mapToLong(api -> (Double) api.get("errorRate") > 0.1 ? 1 : 0)
            .sum();
        long mediumErrorApis = apiDetails.stream()
            .mapToLong(api -> {
                double rate = (Double) api.get("errorRate");
                return rate > 0.05 && rate <= 0.1 ? 1 : 0;
            })
            .sum();
        long lowErrorApis = apiDetails.stream()
            .mapToLong(api -> {
                double rate = (Double) api.get("errorRate");
                return rate > 0 && rate <= 0.05 ? 1 : 0;
            })
            .sum();
        
        classification.put("highErrorApis", highErrorApis);
        classification.put("mediumErrorApis", mediumErrorApis);
        classification.put("lowErrorApis", lowErrorApis);
        
        return classification;
    }

    /**
     * 生成解决方案
     */
    private List<String> generateSolutions(Map<String, Object> problemAnalysis) {
        List<String> solutions = new ArrayList<>();
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> highErrorApis = (List<Map<String, Object>>) problemAnalysis.get("highErrorApis");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> slowApis = (List<Map<String, Object>>) problemAnalysis.get("slowApis");
        
        if (!highErrorApis.isEmpty()) {
            solutions.add("针对高错误率API，建议检查业务逻辑、参数验证和异常处理");
            solutions.add("增加详细的错误日志记录，便于问题定位");
        }
        
        if (!slowApis.isEmpty()) {
            solutions.add("针对慢响应API，建议优化数据库查询、增加索引或使用缓存");
            solutions.add("考虑异步处理或分页查询来提升响应速度");
        }
        
        if (solutions.isEmpty()) {
            solutions.add("当前API运行状态良好，建议继续监控");
        }
        
        return solutions;
    }
}
