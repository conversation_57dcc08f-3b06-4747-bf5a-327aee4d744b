package com.kxpms.bff.web.monitor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.stereotype.Service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * API监控服务
 * 
 * 提供API调用监控、性能统计、异常告警等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiMonitoringService {

    // API调用统计
    private final Map<String, ApiMetrics> apiMetricsMap = new ConcurrentHashMap<>();
    
    // 异常告警配置
    private final Map<String, AlertConfig> alertConfigs = new ConcurrentHashMap<>();
    
    // 告警历史记录
    private final List<AlertRecord> alertHistory = new ArrayList<>();

    /**
     * 记录API调用
     * 
     * @param apiPath API路径
     * @param method HTTP方法
     * @param responseTime 响应时间(毫秒)
     * @param success 是否成功
     * @param errorCode 错误码(如果失败)
     */
    public void recordApiCall(String apiPath, String method, long responseTime, boolean success, String errorCode) {
        String key = method + " " + apiPath;
        
        ApiMetrics metrics = apiMetricsMap.computeIfAbsent(key, k -> new ApiMetrics(k));
        
        // 更新统计数据
        metrics.getTotalCalls().incrementAndGet();
        if (success) {
            metrics.getSuccessCalls().incrementAndGet();
        } else {
            metrics.getFailedCalls().incrementAndGet();
            metrics.getErrorCodes().merge(errorCode, 1L, Long::sum);
        }
        
        // 更新响应时间统计
        updateResponseTimeStats(metrics, responseTime);
        
        // 检查是否需要告警
        checkAlerts(key, metrics);
        
        log.debug("API调用记录: {} - 响应时间: {}ms, 成功: {}", key, responseTime, success);
    }

    /**
     * 更新响应时间统计
     */
    private void updateResponseTimeStats(ApiMetrics metrics, long responseTime) {
        metrics.setLastResponseTime(responseTime);
        metrics.setLastCallTime(LocalDateTime.now());
        
        // 更新最大最小响应时间
        if (responseTime > metrics.getMaxResponseTime()) {
            metrics.setMaxResponseTime(responseTime);
        }
        if (responseTime < metrics.getMinResponseTime() || metrics.getMinResponseTime() == 0) {
            metrics.setMinResponseTime(responseTime);
        }
        
        // 计算平均响应时间
        long totalTime = metrics.getTotalResponseTime() + responseTime;
        metrics.setTotalResponseTime(totalTime);
        metrics.setAvgResponseTime(totalTime / metrics.getTotalCalls().get());
    }

    /**
     * 检查告警条件
     */
    private void checkAlerts(String apiKey, ApiMetrics metrics) {
        AlertConfig config = alertConfigs.get(apiKey);
        if (config == null) {
            return;
        }
        
        // 检查错误率告警
        double errorRate = (double) metrics.getFailedCalls().get() / metrics.getTotalCalls().get();
        if (errorRate > config.getErrorRateThreshold()) {
            triggerAlert(apiKey, "错误率过高", 
                String.format("API %s 错误率 %.2f%% 超过阈值 %.2f%%", 
                    apiKey, errorRate * 100, config.getErrorRateThreshold() * 100));
        }
        
        // 检查响应时间告警
        if (metrics.getLastResponseTime() > config.getResponseTimeThreshold()) {
            triggerAlert(apiKey, "响应时间过长", 
                String.format("API %s 响应时间 %dms 超过阈值 %dms", 
                    apiKey, metrics.getLastResponseTime(), config.getResponseTimeThreshold()));
        }
    }

    /**
     * 触发告警
     */
    public void triggerAlert(String apiKey, String alertType, String message) {
        AlertRecord alert = new AlertRecord();
        alert.setApiKey(apiKey);
        alert.setAlertType(alertType);
        alert.setMessage(message);
        alert.setAlertTime(LocalDateTime.now());
        
        alertHistory.add(alert);
        
        // 记录告警日志
        log.warn("🚨 API告警: {}", message);
        
        // TODO: 发送告警通知 (邮件、短信、钉钉等)
        sendAlertNotification(alert);
    }

    /**
     * 发送告警通知
     */
    private void sendAlertNotification(AlertRecord alert) {
        // TODO: 实现具体的告警通知逻辑
        log.info("发送告警通知: {}", alert.getMessage());
    }

    /**
     * 配置API告警规则
     */
    public void configureAlert(String apiKey, double errorRateThreshold, long responseTimeThreshold) {
        AlertConfig config = new AlertConfig();
        config.setApiKey(apiKey);
        config.setErrorRateThreshold(errorRateThreshold);
        config.setResponseTimeThreshold(responseTimeThreshold);
        
        alertConfigs.put(apiKey, config);
        log.info("配置API告警规则: {} - 错误率阈值: {}%, 响应时间阈值: {}ms", 
            apiKey, errorRateThreshold * 100, responseTimeThreshold);
    }

    /**
     * 获取API统计数据
     */
    public Map<String, Object> getApiStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总体统计
        long totalCalls = apiMetricsMap.values().stream()
            .mapToLong(m -> m.getTotalCalls().get())
            .sum();
        long totalErrors = apiMetricsMap.values().stream()
            .mapToLong(m -> m.getFailedCalls().get())
            .sum();
        
        statistics.put("totalApis", apiMetricsMap.size());
        statistics.put("totalCalls", totalCalls);
        statistics.put("totalErrors", totalErrors);
        statistics.put("overallErrorRate", totalCalls > 0 ? (double) totalErrors / totalCalls : 0.0);
        
        // 详细统计
        List<Map<String, Object>> apiDetails = new ArrayList<>();
        apiMetricsMap.forEach((key, metrics) -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("api", key);
            detail.put("totalCalls", metrics.getTotalCalls().get());
            detail.put("successCalls", metrics.getSuccessCalls().get());
            detail.put("failedCalls", metrics.getFailedCalls().get());
            detail.put("errorRate", metrics.getTotalCalls().get() > 0 ? 
                (double) metrics.getFailedCalls().get() / metrics.getTotalCalls().get() : 0.0);
            detail.put("avgResponseTime", metrics.getAvgResponseTime());
            detail.put("maxResponseTime", metrics.getMaxResponseTime());
            detail.put("minResponseTime", metrics.getMinResponseTime());
            detail.put("lastCallTime", metrics.getLastCallTime());
            
            apiDetails.add(detail);
        });
        
        statistics.put("apiDetails", apiDetails);
        
        return statistics;
    }

    /**
     * 获取告警历史
     */
    public List<AlertRecord> getAlertHistory(int limit) {
        return alertHistory.stream()
            .sorted((a, b) -> b.getAlertTime().compareTo(a.getAlertTime()))
            .limit(limit)
            .toList();
    }

    /**
     * 清理过期数据
     */
    public void cleanupExpiredData() {
        // 清理超过24小时的告警记录
        LocalDateTime cutoff = LocalDateTime.now().minusHours(24);
        alertHistory.removeIf(alert -> alert.getAlertTime().isBefore(cutoff));
        
        log.info("清理过期监控数据完成");
    }

    /**
     * API指标数据
     */
    @Data
    public static class ApiMetrics {
        private String apiKey;
        private AtomicLong totalCalls = new AtomicLong(0);
        private AtomicLong successCalls = new AtomicLong(0);
        private AtomicLong failedCalls = new AtomicLong(0);
        private Map<String, Long> errorCodes = new ConcurrentHashMap<>();
        
        private long totalResponseTime = 0;
        private long avgResponseTime = 0;
        private long maxResponseTime = 0;
        private long minResponseTime = 0;
        private long lastResponseTime = 0;
        private LocalDateTime lastCallTime;
        
        public ApiMetrics(String apiKey) {
            this.apiKey = apiKey;
        }
    }

    /**
     * 告警配置
     */
    @Data
    public static class AlertConfig {
        private String apiKey;
        private double errorRateThreshold = 0.05; // 默认5%错误率阈值
        private long responseTimeThreshold = 5000; // 默认5秒响应时间阈值
    }

    /**
     * 告警记录
     */
    @Data
    public static class AlertRecord {
        private String apiKey;
        private String alertType;
        private String message;
        private LocalDateTime alertTime;
    }
}
