package com.kxpms.bff.web.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.kxpms.bff.web.feign.SystemFeignClient;
import com.kxpms.bff.web.monitor.ApiMonitoringService;
import com.kxpms.common.core.domain.R;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * API健康检查服务
 * 
 * 定期检查各个服务的健康状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApiHealthCheckService {

    @Autowired
    private ApiMonitoringService apiMonitoringService;
    
    @Autowired
    private SystemFeignClient systemFeignClient;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 健康检查结果缓存
    private final Map<String, HealthCheckResult> healthResults = new HashMap<>();
    
    /**
     * 执行全面的健康检查
     */
    @Async
    public CompletableFuture<Map<String, Object>> performFullHealthCheck() {
        log.info("开始执行全面健康检查");
        
        Map<String, Object> result = new HashMap<>();
        List<CompletableFuture<HealthCheckResult>> futures = new ArrayList<>();
        
        // 检查系统服务
        futures.add(checkSystemService());
        
        // 检查其他核心服务
        futures.add(checkServiceHealth("house", "/house/health"));
        futures.add(checkServiceHealth("contract", "/contract/health"));
        futures.add(checkServiceHealth("finance", "/finance/health"));
        futures.add(checkServiceHealth("customer", "/customer/health"));
        
        // 等待所有检查完成
        CompletableFuture<Void> allChecks = CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        );
        
        try {
            allChecks.get(30, TimeUnit.SECONDS); // 30秒超时
            
            // 收集结果
            List<HealthCheckResult> results = new ArrayList<>();
            for (CompletableFuture<HealthCheckResult> future : futures) {
                if (future.isDone() && !future.isCompletedExceptionally()) {
                    results.add(future.get());
                }
            }
            
            // 计算整体健康状态
            result.put("overallStatus", calculateOverallStatus(results));
            result.put("checkTime", LocalDateTime.now());
            result.put("serviceResults", results);
            result.put("totalServices", results.size());
            result.put("healthyServices", results.stream().mapToInt(r -> r.isHealthy() ? 1 : 0).sum());
            
            log.info("全面健康检查完成，检查了 {} 个服务", results.size());
            
        } catch (Exception e) {
            log.error("健康检查执行失败", e);
            result.put("overallStatus", "ERROR");
            result.put("error", e.getMessage());
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 检查系统服务健康状态
     */
    @Async
    public CompletableFuture<HealthCheckResult> checkSystemService() {
        HealthCheckResult result = new HealthCheckResult();
        result.setServiceName("system");
        result.setCheckTime(LocalDateTime.now());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 调用系统服务的健康检查接口
            R<List<Map<String, Object>>> response = systemFeignClient.getDepartmentTree();
            
            long responseTime = System.currentTimeMillis() - startTime;
            result.setResponseTime(responseTime);
            
            if (response != null && response.getCode() == 200) {
                result.setHealthy(true);
                result.setStatus("HEALTHY");
                result.setMessage("系统服务运行正常");
            } else {
                result.setHealthy(false);
                result.setStatus("UNHEALTHY");
                result.setMessage("系统服务响应异常: " + (response != null ? response.getMsg() : "无响应"));
            }
            
        } catch (Exception e) {
            result.setHealthy(false);
            result.setStatus("ERROR");
            result.setMessage("系统服务检查失败: " + e.getMessage());
            result.setResponseTime(5000L); // 设置超时时间
            log.error("系统服务健康检查失败", e);
        }
        
        // 缓存结果
        healthResults.put("system", result);
        
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 检查指定服务的健康状态
     */
    @Async
    public CompletableFuture<HealthCheckResult> checkServiceHealth(String serviceName, String healthPath) {
        HealthCheckResult result = new HealthCheckResult();
        result.setServiceName(serviceName);
        result.setCheckTime(LocalDateTime.now());
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 构建健康检查URL
            String url = "http://" + serviceName + healthPath;
            
            // 执行健康检查
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            
            long responseTime = System.currentTimeMillis() - startTime;
            result.setResponseTime(responseTime);
            
            if (response != null) {
                result.setHealthy(true);
                result.setStatus("HEALTHY");
                result.setMessage(serviceName + " 服务运行正常");
                result.setDetails(response);
            } else {
                result.setHealthy(false);
                result.setStatus("UNHEALTHY");
                result.setMessage(serviceName + " 服务无响应");
            }
            
        } catch (Exception e) {
            result.setHealthy(false);
            result.setStatus("ERROR");
            result.setMessage(serviceName + " 服务检查失败: " + e.getMessage());
            result.setResponseTime(5000L);
            log.warn("{} 服务健康检查失败: {}", serviceName, e.getMessage());
        }
        
        // 缓存结果
        healthResults.put(serviceName, result);
        
        return CompletableFuture.completedFuture(result);
    }
    
    /**
     * 定时健康检查 - 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void scheduledHealthCheck() {
        log.info("开始定时健康检查");
        
        try {
            CompletableFuture<Map<String, Object>> future = performFullHealthCheck();
            Map<String, Object> result = future.get(60, TimeUnit.SECONDS);
            
            String overallStatus = (String) result.get("overallStatus");
            Integer totalServices = (Integer) result.get("totalServices");
            Integer healthyServices = (Integer) result.get("healthyServices");
            
            log.info("定时健康检查完成 - 状态: {}, 健康服务: {}/{}", 
                overallStatus, healthyServices, totalServices);
                
            // 如果有服务不健康，记录告警
            if (healthyServices < totalServices) {
                apiMonitoringService.triggerAlert("HEALTH_CHECK", "服务健康检查", 
                    String.format("有 %d 个服务不健康，总共 %d 个服务", 
                        totalServices - healthyServices, totalServices));
            }
            
        } catch (Exception e) {
            log.error("定时健康检查失败", e);
        }
    }
    
    /**
     * 获取缓存的健康检查结果
     */
    public Map<String, HealthCheckResult> getCachedHealthResults() {
        return new HashMap<>(healthResults);
    }
    
    /**
     * 计算整体健康状态
     */
    private String calculateOverallStatus(List<HealthCheckResult> results) {
        if (results.isEmpty()) {
            return "UNKNOWN";
        }
        
        long healthyCount = results.stream().mapToLong(r -> r.isHealthy() ? 1 : 0).sum();
        double healthyRatio = (double) healthyCount / results.size();
        
        if (healthyRatio == 1.0) {
            return "HEALTHY";
        } else if (healthyRatio >= 0.8) {
            return "DEGRADED";
        } else if (healthyRatio >= 0.5) {
            return "UNHEALTHY";
        } else {
            return "CRITICAL";
        }
    }
    
    /**
     * 健康检查结果
     */
    @Data
    public static class HealthCheckResult {
        private String serviceName;
        private boolean healthy;
        private String status;
        private String message;
        private long responseTime;
        private LocalDateTime checkTime;
        private Map<String, Object> details;
    }
}
