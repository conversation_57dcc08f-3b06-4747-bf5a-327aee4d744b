#!/bin/bash

# PMS Cloud 主要服务部署脚本
# 用于部署核心业务服务（Gateway、BFF、BLL、Modules等）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} ${timestamp} - $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} ${timestamp} - $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} ${timestamp} - $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message"
            ;;
    esac
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

log "INFO" "脚本目录: $SCRIPT_DIR"
log "INFO" "项目根目录: $PROJECT_ROOT"

# 端口配置 (兼容bash 3.2)
get_port() {
    case $1 in
        "gateway") echo "9600" ;;
        "web-bff") echo "9303" ;;
        "auth-bff") echo "9301" ;;
        "system") echo "9201" ;;
        "house") echo "9202" ;;
        "customer") echo "9203" ;;
        "contract") echo "9204" ;;
        "finance") echo "9205" ;;
        "workflow") echo "9206" ;;
        *) echo "" ;;
    esac
}

# 服务列表
SERVICES="gateway web-bff auth-bff system house customer contract finance workflow"

# 检查中间件服务
check_middleware() {
    log "INFO" "检查中间件服务状态..."
    
    local mysql_ready=false
    local redis_ready=false
    local nacos_ready=false
    
    # 检查MySQL
    if docker exec pms-mysql mysqladmin ping -h localhost -uroot -p'Veritrans&9900#' >/dev/null 2>&1; then
        mysql_ready=true
        log "SUCCESS" "MySQL服务正常"
    else
        log "ERROR" "MySQL服务未就绪"
    fi
    
    # 检查Redis
    if docker exec pms-redis redis-cli ping >/dev/null 2>&1; then
        redis_ready=true
        log "SUCCESS" "Redis服务正常"
    else
        log "ERROR" "Redis服务未就绪"
    fi
    
    # 检查Nacos
    if curl -s http://localhost:8848/nacos/actuator/health >/dev/null 2>&1; then
        nacos_ready=true
        log "SUCCESS" "Nacos服务正常"
    else
        log "ERROR" "Nacos服务未就绪"
    fi
    
    if [ "$mysql_ready" = false ] || [ "$redis_ready" = false ] || [ "$nacos_ready" = false ]; then
        log "ERROR" "中间件服务未完全启动，请先运行 ./quick-start.sh"
        exit 1
    fi
}

# 检查端口占用
check_ports() {
    log "INFO" "检查端口占用情况..."
    
    local occupied_ports=""

    for service in $SERVICES; do
        local port=$(get_port $service)
        if [ -n "$port" ] && lsof -i :$port >/dev/null 2>&1; then
            occupied_ports="$occupied_ports $service:$port"
        fi
    done
    
    if [ -n "$occupied_ports" ]; then
        log "WARNING" "以下端口被占用:"
        for port_info in $occupied_ports; do
            log "WARNING" "  $port_info"
        done
        log "INFO" "将尝试停止相关服务..."
    fi
}

# 编译项目
compile_projects() {
    log "INFO" "编译项目..."
    
    cd "$PROJECT_ROOT"
    
    # 编译common模块
    log "INFO" "编译common模块..."
    cd common/common-security
    mvn clean install -DskipTests -Dmaven.test.skip=true
    cd ../..
    
    # 编译gateway
    log "INFO" "编译gateway..."
    cd gateway
    mvn clean package -DskipTests -Dmaven.test.skip=true
    cd ..
    
    # 编译BFF模块
    log "INFO" "编译BFF模块..."
    cd bff/web-bff
    mvn clean package -DskipTests -Dmaven.test.skip=true
    if [ $? -ne 0 ]; then
        log "ERROR" "web-bff编译失败"
        return 1
    fi
    cd ../auth-bff
    mvn clean package -DskipTests -Dmaven.test.skip=true
    if [ $? -ne 0 ]; then
        log "ERROR" "auth-bff编译失败"
        return 1
    fi
    cd ../..
    
    # 编译BLL模块
    log "INFO" "编译BLL模块..."
    cd bll/bll-house
    mvn clean package -DskipTests -Dmaven.test.skip=true
    if [ $? -ne 0 ]; then
        log "ERROR" "bll-house编译失败"
        return 1
    fi
    cd ../bll-customer
    mvn clean package -DskipTests -Dmaven.test.skip=true
    if [ $? -ne 0 ]; then
        log "ERROR" "bll-customer编译失败"
        return 1
    fi
    cd ../..
    
    log "SUCCESS" "项目编译完成"
}

# 启动服务
start_service() {
    local service_name=$1
    local service_path=$2
    local jar_name=$3
    local port=$(get_port $service_name)
    
    log "INFO" "启动服务: $service_name (端口: $port)"
    
    cd "$PROJECT_ROOT/$service_path"
    
    # 检查jar文件是否存在
    if [ ! -f "target/$jar_name" ]; then
        log "ERROR" "JAR文件不存在: target/$jar_name"
        return 1
    fi
    
    # 停止已存在的进程
    local pid=$(lsof -ti:$port 2>/dev/null || true)
    if [ -n "$pid" ]; then
        log "WARNING" "停止端口 $port 上的进程: $pid"
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
    
    # 启动服务
    nohup java -jar target/$jar_name --spring.profiles.active=dev > "$SCRIPT_DIR/logs/${service_name}-${port}.log" 2>&1 &
    local service_pid=$!
    
    log "INFO" "服务 $service_name 已启动，PID: $service_pid"
    
    # 等待服务启动
    local max_wait=60
    local wait_time=0
    
    while [ $wait_time -lt $max_wait ]; do
        if curl -s http://localhost:$port/actuator/health >/dev/null 2>&1; then
            log "SUCCESS" "服务 $service_name 启动成功 (端口: $port)"
            return 0
        fi
        sleep 2
        wait_time=$((wait_time + 2))
    done
    
    log "WARNING" "服务 $service_name 启动超时，请检查日志: $SCRIPT_DIR/logs/${service_name}-${port}.log"
    return 1
}

# 启动所有服务
start_all_services() {
    log "INFO" "开始启动所有服务..."
    
    # 创建日志目录
    mkdir -p "$SCRIPT_DIR/logs"
    
    # 按依赖顺序启动服务
    
    # 1. 启动Gateway
    start_service "gateway" "gateway" "gateway-1.0.0.jar"
    
    # 2. 启动BLL服务
    start_service "system" "bll/bll-system" "bll-system-1.0.0.jar"
    start_service "house" "bll/bll-house" "bll-house-1.0.0.jar"
    
    # 3. 启动BFF服务
    start_service "auth-bff" "bff/auth-bff" "auth-bff-1.0.0.jar"
    start_service "web-bff" "bff/web-bff" "web-bff-1.0.0.jar"
    
    log "SUCCESS" "所有服务启动完成"
}

# 显示服务状态
show_service_status() {
    log "INFO" "服务状态检查..."
    
    echo ""
    echo "=== 服务状态 ==="
    printf "%-15s %-8s %-10s %-20s\n" "服务名称" "端口" "状态" "健康检查"
    echo "--------------------------------------------------------"
    
    for service in $SERVICES; do
        local port=$(get_port $service)
        local status="停止"
        local health="N/A"
        
        if lsof -i :$port >/dev/null 2>&1; then
            status="运行"
            if curl -s http://localhost:$port/actuator/health >/dev/null 2>&1; then
                health="健康"
            else
                health="异常"
            fi
        fi
        
        printf "%-15s %-8s %-10s %-20s\n" "$service" "$port" "$status" "$health"
    done
    
    echo ""
    echo "=== 访问地址 ==="
    echo "Gateway:   http://localhost:9600"
    echo "Web BFF:   http://localhost:9303"
    echo "Auth BFF:  http://localhost:9301"
    echo ""
    echo "=== 日志文件 ==="
    echo "日志目录: $SCRIPT_DIR/logs/"
    echo ""
}

# 测试核心接口
test_core_apis() {
    log "INFO" "测试核心接口..."
    
    # 测试Gateway健康检查
    if curl -s http://localhost:9600/actuator/health >/dev/null 2>&1; then
        log "SUCCESS" "Gateway健康检查通过"
    else
        log "ERROR" "Gateway健康检查失败"
    fi
    
    # 测试验证码接口
    if curl -s http://localhost:9600/api/bff/auth/v3/captcha >/dev/null 2>&1; then
        log "SUCCESS" "验证码接口测试通过"
    else
        log "WARNING" "验证码接口测试失败，可能需要等待服务完全启动"
    fi
}

# 主函数
main() {
    log "INFO" "开始部署PMS Cloud主要服务..."
    
    check_middleware
    check_ports
    compile_projects
    start_all_services
    show_service_status
    test_core_apis
    
    log "SUCCESS" "PMS Cloud主要服务部署完成！"
    log "INFO" "可以通过 http://localhost:9600 访问系统"
}

# 执行主函数
main "$@"
