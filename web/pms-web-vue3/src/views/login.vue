<template>
  <div id="loginContainer" class="login-user">
    <div class="login-container">
      <!-- Logo -->
      <img class="login-logo" src="/images/logo_opacity.png" />

      <!-- 登录表单 -->
      <div class="login-form-container">
        <div class="login-form-box">
          <h1 class="login-title">登录到</h1>
          <h2 class="login-subtitle">建悦里住房租赁资产管理平台</h2>

          <div class="login-form">
            <div class="form-item">
              <input
                v-model="loginForm.username"
                type="text"
                class="form-input"
                placeholder="用户名"
                @keyup.enter="handleLogin"
              />
            </div>
            <div class="form-item">
              <input
                v-model="loginForm.password"
                type="password"
                class="form-input"
                placeholder="密码"
                @keyup.enter="handleLogin"
              />
            </div>
            <div class="form-item captcha-container">
              <input
                v-model="loginForm.captchaValue"
                type="text"
                class="form-input captcha-input"
                placeholder="验证码"
                @keyup.enter="handleLogin"
              />
              <img
                v-if="captchaUrl"
                :src="captchaUrl"
                class="captcha-image"
                @click="refreshCaptcha"
                alt="点击刷新验证码"
              />
            </div>
            <button
              class="login-button"
              :disabled="isLogging"
              @click="handleLogin"
            >
              {{ isLogging ? "登录中..." : "登录" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type LoginParams } from "@/utils/satoken-auth";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import request from "@/utils/request";
import {API_ENDPOINTS} from "@/config/api-config";

const router = useRouter();
const route = useRoute();
const isLogging = ref(false);
const captchaUrl = ref("");
const captchaKey = ref("");

// 登录表单数据
const loginForm = reactive<LoginParams>({
  username: "",
  password: "",
  tenantId: "2", // 默认租户ID
  captcha: "",
  captchaKey: "",
  rememberMe: true
});

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const response = await request.get(API_ENDPOINTS.AUTH.CAPTCHA);
    if (response.data) {
      captchaUrl.value = "data:image/png;base64," + response.data.img;
      captchaKey.value = response.data.key;
      loginForm.captchaKey = response.data.key;
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    ElMessage.warning('获取验证码失败');
  }
};

// 回车登录
const handleKeyPress = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    handleLogin();
  }
};

// 登录处理
const handleLogin = async () => {
  if (isLogging.value) return;

  // 验证必填字段
  if (!loginForm.username) {
    ElMessage.warning("请输入用户名");
    return;
  }
  if (!loginForm.password) {
    ElMessage.warning("请输入密码");
    return;
  }
  if (!loginForm.captcha) {
    ElMessage.warning("请输入验证码");
    return;
  }

  isLogging.value = true;

  try {
    // 设置验证码相关参数
    loginForm.captchaKey = captchaKey.value;

    // 使用SaToken进行登录
    const result = await saTokenAuth.login(loginForm);

    ElNotification.success({
      title: '登录成功',
      message: `欢迎回来，${result.user.username}！`,
      duration: 3000
    });

    // 跳转到目标页面
    const redirect = route.query.redirect as string || '/';
    await router.push(redirect);

  } catch (error: any) {
    console.error('登录失败:', error);

    // 如果是验证码错误，显示验证码
    if (error.message?.includes('验证码') || error.response?.status === 429) {
      await refreshCaptcha();
    }

    ElMessage.error(error.message || '登录失败，请检查用户名和密码');
  } finally {
    isLogging.value = false;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  // 如果已经登录，直接跳转
  if (saTokenAuth.isLoggedIn()) {
    const isValid = await saTokenAuth.checkToken();
    if (isValid) {
      const redirect = route.query.redirect as string || '/';
      router.push(redirect);
      return;
    } else {
      saTokenAuth.clearAuth();
    }
  }

  // 获取验证码
  refreshCaptcha();

  // 开发环境下自动填充测试数据
  if (import.meta.env?.DEV) {
    loginForm.username = 'admin';
    loginForm.password = 'admin123';
  }
});
</script>

<style scoped>
.login-user {
  width: 100vw;
  height: 100vh;
  background: url("@/assets/images/login_bg_back.png") no-repeat center center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  overflow: hidden;
  border-radius: 10px;
  width: 1100px;
  height: 600px;
  display: flex;
  align-items: center;
  position: relative;
  background: url("@/assets/images/login_bg_front.png") no-repeat center center;
  background-size: cover;
}

.login-logo {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 180px;
}

.login-form-container {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}

.login-form-box {
  width: 400px;
  padding: 40px;
}

.login-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
  font-weight: normal;
}

.login-subtitle {
  font-size: 20px;
  color: #333;
  margin-bottom: 40px;
  font-weight: bold;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 20px;
}

.form-input {
  width: 100%;
  height: 50px;
  padding: 0 15px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.3s;
}

.form-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 185, 107, 0.1);
}

.form-input::placeholder {
  color: #999;
}

.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-input {
  width: 60%;
}

.captcha-image {
  height: 50px;
  border-radius: 4px;
  cursor: pointer;
}

.login-button {
  width: 100%;
  height: 50px;
  background: var(--color-primary);
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.login-button:hover {
  opacity: 0.9;
}

.login-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .left-image {
    display: none;
  }

  .login-form-container {
    width: 100%;
  }

  .login-form-box {
    width: 90%;
    max-width: 400px;
  }
}
</style>
